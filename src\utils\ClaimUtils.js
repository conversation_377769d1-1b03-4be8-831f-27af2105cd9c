/**
 * Populate claims table with appropriate description
 * @param {Object} claims ID token claims
 * @returns claimsObject
 */
export const createClaimsTable = (claims) => {
    let claimsObj = {};
    let index = 0;

    Object.keys(claims).forEach((key) => {
        if (typeof claims[key] !== 'string' && typeof claims[key] !== 'number') return;
        let description;
        switch (key) {
            case 'aud':
                description = "Identifies the intended recipient of the token. In ID tokens, the audience is your app's Application ID, assigned to your app in the Azure portal.";
                break;
            case 'iss':
                description = "Identifies the issuer, or authorization server that constructs and returns the token. It also identifies the Azure AD tenant for which the user was authenticated. If the token was issued by the v2.0 endpoint, the URI will end in /v2.0. The GUID that indicates that the user is a consumer user from a Microsoft account is 9188040d-6c67-4c5b-b112-36a304b66dad.";
                break;
            case 'iat':
                description = "Issued At indicates when the authentication for this token occurred.";
                break;
            case 'nbf':
                description = "The nbf (not before) claim identifies the time (as UNIX timestamp) before which the JWT must not be accepted for processing.";
                break;
            case 'exp':
                description = "The exp (expiration time) claim identifies the expiration time (as UNIX timestamp) on or after which the JWT must not be accepted for processing. It's important to note that in certain circumstances, a resource may reject the token before this time. For example, if a change in authentication is required or a token revocation has been detected.";
                break;
            case 'name':
                description = "The name claim provides a human-readable value that identifies the subject of the token. The value isn't guaranteed to be unique, it can be changed, and it's designed to be used only for display purposes. The profile scope is required to receive this claim.";
                break;
            case 'preferred_username':
                description = "The primary username that represents the user. It could be an email address, phone number, or a generic username without a specified format. Its value is mutable and might change over time. Since it is mutable, this value must not be used to make authorization decisions. It can be used for username hints, however, and in human-readable UI as a username. The profile scope is required in order to receive this claim.";
                break;
            case 'nonce':
                description = "The nonce matches the parameter included in the original /authorize request to the IDP. If it does not match, your application should reject the token.";
                break;
            case 'oid':
                description = "The oid (user’s object id) is the only claim that should be used to uniquely identify a user in an Azure AD tenant. The token might have one or more of the following claim, that might seem like a unique identifier, but is not and should not be used as such.";
                break;
            case 'tid':
                description = "The tenant ID. You will use this claim to ensure that only users from the current Azure AD tenant can access this app.";
                break;
            case 'upn':
                description = "(user principal name) – might be unique amongst the active set of users in a tenant but tend to get reassigned to new employees as employees leave the organization and others take their place or might change to reflect a personal change like marriage.";
                break;
            case 'email':
                description = "Email might be unique amongst the active set of users in a tenant but tend to get reassigned to new employees as employees leave the organization and others take their place.";
                break;
            case 'acct':
                description = "Available as an optional claim, it lets you know what the type of user (homed, guest) is. For example, for an individual’s access to their data you might not care for this claim, but you would use this along with tenant id (tid) to control access to say a company-wide dashboard to just employees (homed users) and not contractors (guest users).";
                break;
            case 'sid':
                description = "Session ID, used for per-session user sign-out.";
                break;
            case 'sub':
                description = "The sub claim is a pairwise identifier - it is unique to a particular application ID. If a single user signs into two different apps using two different client IDs, those apps will receive two different values for the subject claim.";
                break;
            case 'ver':
                description = "Version of the token issued by the Microsoft identity platform";
                break;
            case 'uti':
            case 'rh':
            case '_claim_names':
            case '_claim_sources':
            default:
                description = "";
        }
        populateClaim(key, claims[key], description, index, claimsObj);
        index++;
    });

    return claimsObj;
};

/**
 * Populates claim, description, and value into an claimsObject
 * @param {String} claim
 * @param {String} value
 * @param {String} description
 * @param {Number} index
 * @param {Object} claimsObject
 */
const populateClaim = (claim, value, description, index, claimsObject) => {
    let claimsArray = [];
    claimsArray[0] = claim;
    claimsArray[1] = value;
    claimsArray[2] = description;
    claimsObject[index] = claimsArray;
};

/**
 * Transforms Unix timestamp to date and returns a string value of that date
 * @param {String} date Unix timestamp
 * @returns
 */
const changeDateFormat = (date) => {
    let dateObj = new Date(date * 1000);
    return `${date} - [${dateObj.toString()}]`;
};
