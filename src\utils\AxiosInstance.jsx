import axios from 'axios'


export const apiClient = (() => {
    let instance = null;

    const createInstance = () => {
        return axios.create({
            baseURL: import.meta.env.VITE_API_URL
        });
    };

    return {
        getInstance: () => {
            if (!instance) {
                instance = createInstance();
            }
            return instance;
        },
    };
})();



