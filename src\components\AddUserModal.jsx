import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Spinner, ToggleSwitch} from "flowbite-react";
import {CountriesDropdownValues} from "./CountriesDropdownValues.jsx";
import React, {useState} from "react";
import {addUser} from '../services/UsersService.jsx';
import {UserAddedCard} from "./UserAddedCard.jsx";

export const AddUserModal = (props) => {

  const [deUserCheckbox, setDeUserCheckbox] = useState(true);
  const [dfUserCheckbox, setDfUserCheckbox] = useState(false);
  const [userAdded, setUserAdded] = useState(false);

  const [formHidden, setFormHidden] = useState(false);

  const [newUser, setNewUser] = useState({});

  const handleNewUserForm = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    setFormHidden(true);
    addUser(props.instance, {
      "email": formData.get("email"),
      "fullName": formData.get("fullName"),
      "country": formData.get("country"),
      "deUser": deUserCheckbox,
      "dfUser": dfUserCheckbox,
    })
    .then((response) => {
      setUserAdded(true);
      setNewUser(response.data);
    }).catch((e) => {
      setFormHidden(false);
    });
  }

  const reset = () => {
    document.getElementById("new-user-form").reset();
    setFormHidden(false);
    setUserAdded(false)
  }

  return (
      <>
      <Modal show={props.openModal} onClose={() => props.setOpenModal(false)}>
        <Modal.Header>Add new user</Modal.Header>
        <Modal.Body>
          {userAdded ?
              <UserAddedCard user={newUser} />
              : ''}
          <form hidden={formHidden} id="new-user-form" className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4" onSubmit={handleNewUserForm}>
            <div className="mb-5">
              <FloatingLabel variant="outlined" label="Email" color="default" name="email" className="mb-2" required={true}/>
              <FloatingLabel variant="outlined" label="Full Name" color="default" name="fullName" className="mb-2" required={true}/>
              <Select name="country" required placeholder="Country" className="mb-2">
                <CountriesDropdownValues/>
              </Select>
            </div>
            <div className="flex max-w-md flex-col gap-4">
              <ToggleSwitch name="deUser" checked={deUserCheckbox} onChange={setDeUserCheckbox} label="Dairy Discover"/>
              <ToggleSwitch name="dfUser" checked={dfUserCheckbox} onChange={setDfUserCheckbox} label="Dairy Forecast"/>
            </div>
          </form>
          {formHidden && !userAdded ?
              <div className="text-center">
                <Spinner aria-label="Extra large spinner example" size="xl"/>
              </div>
              : ''}
        </Modal.Body>
        <Modal.Footer>
          {!formHidden ? <Button form="new-user-form" type="submit">Add user</Button> : ''}
          {userAdded ? <Button onClick={reset}>Add another</Button> : ''}
          {userAdded ? <Button onClick={() => {reset(); props.setOpenModal(false);}} color="gray">Close</Button> : ''}
        </Modal.Footer>
      </Modal>
      </>
  );
}