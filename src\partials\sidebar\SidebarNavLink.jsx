import {NavLink} from 'react-router-dom';

function SidebarNavLink({path, label}) {

  const linkActiveClass = ({isActive}) =>
    'block transition duration-150 truncate ' + (isActive ? 'text-indigo-500' : 'text-slate-400 hover:text-slate-200');

  return (
    <li className="mb-1 last:mb-0">
      <NavLink end to={path} className={linkActiveClass}>
        <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
          {label}
        </span>
      </NavLink>
    </li>
  );
}

export default SidebarNavLink;