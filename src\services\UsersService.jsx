import {apiClient} from '../utils/AxiosInstance.jsx';
import {getToken} from './TokenService.jsx';

export const fetchUsers = async (msalInstance) => {
  return getToken(msalInstance).then(
    (token) => {
      try {
        return apiClient.getInstance().get('/user', {headers: {Authorization: "Bearer " + token}}); // This will include the response data, status, and other information
      } catch (error) {
        // Handle or throw the error as needed
        console.error('Error fetching users:', error);
        throw error;
      }
    }
  )
};

export const addUser = async (msalInstance, user) => {
  return getToken(msalInstance).then(
      (token) => {
        try {
          return apiClient.getInstance().post(
              '/user',
              user, {
                headers: {
                  Authorization: "Bearer " + token,
                  "Content-Type": "application/json",
                  Accept: "application/json",
                }
              }); // This will include the response data, status, and other information
        } catch (error) {
          // Handle or throw the error as needed
          console.error('Error fetching users:', error);
          throw error;
        }
      }
  )
};

export const getUserDetails = async (msalInstance, deAppId, dfAppEmail) => {
  return getToken(msalInstance).then(
      (token) => {
        try {
          return apiClient.getInstance().get(
              '/user-details',
              {
                headers: {Authorization: "Bearer " + token},
                params: {
                  deAppId: deAppId,
                  dfAppEmail: dfAppEmail
                }
              });
        } catch (error) {
          console.error('Error getting user details:', error);
          throw error;
        }
      }
  )
};

export const updateDeAppUser = async (msalInstance, uuid, userData) => {
  return getToken(msalInstance).then(
      (token) => {
        try {
          return apiClient.getInstance().patch(
              '/user/de/' + uuid,
              userData, {
                headers: {
                  Authorization: "Bearer " + token,
                  "Content-Type": "application/json",
                  Accept: "application/json",
                }
              });
        } catch (error) {
          console.error('Error updating users:', error);
          throw error;
        }
      }
  )
};

export const deleteDfUser = async (msalInstance, email) => {
  return getToken(msalInstance).then(
      (token) => {
        try {
          return apiClient.getInstance().delete(
              '/user/df/' + email, {
                headers: {
                  Authorization: "Bearer " + token,
                  "Content-Type": "application/json",
                  Accept: "application/json",
                }
              });
        } catch (error) {
          console.error('Error deleting DF user:', error);
          throw error;
        }
      }
  )
};
