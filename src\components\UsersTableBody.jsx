import {decode} from "html-entities";
import React, {useState} from "react";
import {UserDetailsModal} from "./UserDetailsModal.jsx";
import {instantToSimpleDate} from "../utils/TimeUtils.jsx";

const TableBody = ({ tableData, instance }) => {
  const [openUserDetailsModal, setUserDetailsModal] = useState(false);
  const [userLoading, setUserLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState({
    deAppData: {},
    dfAppData: {},
  });

  function renderStatus(primaryColor, secondaryColor) {
    return (
        <>
          <svg xmlns="http://www.w3.org/2000/svg" height="20px"
               width="20px" version="1.1" id="statusImage" viewBox="0 0 20 20"
               enableBackground="new 0 0 20 20">
            <g>
              <circle fill={secondaryColor} cx="10" cy="10" r="10"/>
              <circle fill={primaryColor} cx="10" cy="10" r="8"/>
            </g>
          </svg>
        </>
    )
  }

  function openUserDetailsModalHandler(user) {
    console.log("user table body", user);
    setSelectedUser(user);
    setUserLoading(true);
    setUserDetailsModal(true);
  }

  return (
      <>
        <UserDetailsModal setOpenModal={setUserDetailsModal} openModal={openUserDetailsModal} instance={instance} selectedUser={selectedUser}
                          userLoading={userLoading} setUserLoading={setUserLoading} />
      <tbody
          className="text-sm divide-y divide-slate-100 dark:divide-slate-700">
      {

        tableData.map(deUser => {
          return (
              <tr key={deUser.uuid}>
                <td className="p-2">
                  <div className="text-left font-medium text-slate-800 dark:text-slate-100">
                    <a href="#" onClick={() => openUserDetailsModalHandler(deUser)}>{decode(deUser.name)}</a>
                  </div>
                </td>
                <td className="p-2">
                <div className="text-left"><a href="#" onClick={() => openUserDetailsModalHandler(deUser)}>{deUser.email}</a></div>
                </td>
                <td className="p-2 whitespace-nowrap">
                  <div className="text-left font-medium text-green-500">
                    {renderStatus(deUser.statusColor.de.primary, deUser.statusColor.de.secondary)}
                  </div>
                </td>
                <td className="p-2 whitespace-nowrap">
                  <div className="text-left font-medium text-green-500">
                    {renderStatus(deUser.statusColor.df.primary, deUser.statusColor.df.secondary)}
                  </div>
                </td>
                <td className="p-2 whitespace-nowrap">
                  <div
                      className="text-center">{deUser.country}</div>
                </td>
                <td className="p-2 whitespace-nowrap">
                  <div
                      className="text-center">{instantToSimpleDate(deUser.last_login)}</div>
                </td>
                <td className="p-2 whitespace-nowrap">
                  <div
                      className="text-center">{deUser.ver}</div>
                </td>
                <td className="p-2">
                  <div
                      className="text-center whitespace-pre-wrap w-28">{deUser.deAppData.deviceType} {deUser.deAppData.deviceModel}</div>
                </td>
              </tr>
          )
        })
      }
      </tbody>
      </>
  );
};

export default TableBody;
