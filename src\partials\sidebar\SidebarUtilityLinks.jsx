import React from 'react';
import SidebarLinkGroup from '../SidebarLinkGroup.jsx';
import SidebarNavLink from './SidebarNavLink.jsx';

function SidebarUtilityLinks({pathname, sidebarExpanded, setSidebarExpanded}) {
  return (
    <SidebarLinkGroup activecondition={pathname.includes('utility')}>
      {(handleClick, open) => {
        return (
          <React.Fragment>
            <a
              href="#"
              className={`block text-slate-200 truncate transition duration-150 ${
                pathname.includes('utility') ? 'hover:text-slate-200' : 'hover:text-white'
              }`}
              onClick={(e) => {
                e.preventDefault();
                sidebarExpanded ? handleClick() : setSidebarExpanded(true);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                    <circle
                      className={`fill-current ${pathname.includes('utility') ? 'text-indigo-300' : 'text-slate-400'}`}
                      cx="18.5"
                      cy="5.5"
                      r="4.5"
                    />
                    <circle
                      className={`fill-current ${pathname.includes('utility') ? 'text-indigo-500' : 'text-slate-600'}`}
                      cx="5.5"
                      cy="5.5"
                      r="4.5"
                    />
                    <circle
                      className={`fill-current ${pathname.includes('utility') ? 'text-indigo-500' : 'text-slate-600'}`}
                      cx="18.5"
                      cy="18.5"
                      r="4.5"
                    />
                    <circle
                      className={`fill-current ${pathname.includes('utility') ? 'text-indigo-300' : 'text-slate-400'}`}
                      cx="5.5"
                      cy="18.5"
                      r="4.5"
                    />
                  </svg>
                  <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                              Utility
                            </span>
                </div>
                {/* Icon */}
                <div className="flex shrink-0 ml-2">
                  <svg className={`w-3 h-3 shrink-0 ml-1 fill-current text-slate-400 ${open && 'rotate-180'}`} viewBox="0 0 12 12">
                    <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
                  </svg>
                </div>
              </div>
            </a>
            <div className="lg:hidden lg:sidebar-expanded:block 2xl:block">
              <ul className={`pl-9 mt-1 ${!open && 'hidden'}`}>
                <SidebarNavLink path="/utility/changelog" label="Changelog"></SidebarNavLink>
                <SidebarNavLink path="/utility/roadmap" label="Roadmap"></SidebarNavLink>
                <SidebarNavLink path="/utility/faqs" label="FAQs"></SidebarNavLink>
                <SidebarNavLink path="/utility/empty-state" label="Empty State"></SidebarNavLink>
                <SidebarNavLink path="/utility/404" label="404"></SidebarNavLink>
                <SidebarNavLink path="/utility/knowledge-base" label="Knowledge Base"></SidebarNavLink>
              </ul>
            </div>
          </React.Fragment>
        );
      }}
    </SidebarLinkGroup>
  );
}

export default SidebarUtilityLinks;