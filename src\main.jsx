import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import ThemeProvider from './utils/ThemeContext';
import App from './App';

import {msalConfig} from './config/authConfig.js';
import {EventType, PublicClientApplication} from '@azure/msal-browser';
import {getToken} from './services/TokenService.jsx';

/**
 * MSAL should be instantiated outside of the component tree to prevent it from being re-instantiated on re-renders.
 * For more, visit: https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-react/docs/getting-started.md
 */
const msalInstance = new PublicClientApplication(msalConfig);
await msalInstance.initialize();

// Listen for sign-in event and set active account
msalInstance.addEventCallback((event) => {
  if (event.eventType === EventType.LOGIN_SUCCESS && event.payload.account) {
    const account = event.payload.account;
    msalInstance.setActiveAccount(account);
  }
});

await getToken(msalInstance);


ReactDOM.createRoot(document.getElementById('root')).render(
    <Router>
      <ThemeProvider>
        <App msalInstance={msalInstance}/>
      </ThemeProvider>
    </Router>
);
