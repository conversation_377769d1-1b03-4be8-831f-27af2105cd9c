import { useState } from "react";

function getDefaultSorting(defaultTableData, columns) {
  const sorted = [...defaultTableData].sort((a, b) => {
    const filterColumn = columns.filter((column) => column.sortable);

    // Merge all array objects into single object and extract accessor and sortbyOrder keys
    let { accessor = "uuid", sortbyOrder = "asc" } = Object.assign(
        {},
        ...filterColumn
    );

    if (a[accessor] === null) return 1;
    if (b[accessor] === null) return -1;
    if (a[accessor] === null && b[accessor] === null) return 0;

    const ascending = a[accessor]
    .toString()
    .localeCompare(b[accessor].toString(), "en", {
      numeric: true,
    });

    return sortbyOrder === "asc" ? ascending : -ascending;
  });
  return sorted;
}

export const useSortableTable = (data, columns) => {
  const [tableData, setTableData] = useState(getDefaultSorting(data, columns));

  const handleSorting = (sortField, sortOrder) => {
    if (sortField) {
      const sorted = [...tableData].sort((a, b) => {
        if (a[sortField] === null) return 1;
        if (b[sortField] === null) return -1;
        if (a[sortField] === null && b[sortField] === null) return 0;
        
        const [aField, bField] = sortFieldsValues(sortField, a, b);

        return (
            aField.localeCompare(bField, "en", {
              numeric: true,
            }) * (sortOrder === "asc" ? 1 : -1)
        );
      });
      setTableData(sorted);
    }
  };

  const sortFieldsValues = (sortField, a, b) => {
    const aField = sortField === 'last_login' ? a[sortField].getTime().toString() : a[sortField].toString();
    const bField = sortField === 'last_login' ? b[sortField].getTime().toString() : b[sortField].toString();
    return [aField, bField]
  }

  return [tableData, handleSorting, setTableData];
};