import React, {useEffect, useState} from 'react';
import {useMsal} from '@azure/msal-react';

import {fetchUsers} from '../services/UsersService.jsx';
import DashboardAvatars from './dashboard/DashboardAvatars.jsx';
import {CountriesDropdownValues} from "../components/CountriesDropdownValues.jsx";
import {useSortableTable} from "../utils/UserSortableTable.jsx";
import UsersTableHead from "../components/UsersTableHead.jsx";
import UsersTableBody from "../components/UsersTableBody.jsx";
import {hasWritePermission} from "../services/TokenService.jsx";
import {AddUserButton} from "../components/AddUserButton.jsx";

export const UsersList = () => {
  const {instance} = useMsal();
  const [deUsers, setDeUsers] = useState([]);
  const [deChecked, setDeChecked] = useState(false);
  const [dfChecked, setDfChecked] = useState(false);
  const [countryFilter, setCountryFilter] = useState("");

  const [searchItem, setSearchItem] = useState('')
  const [filteredUsers, setFilteredUsers] = useState(deUsers)

  const columns = [
    {label: "Name", accessor: "name", sortable: true},
    {label: "Email", accessor: "email", sortable: true},
    {label: "DE", accessor: "de", sortable: false},
    {label: "DF", accessor: "df", sortable: false},
    {label: "Country", accessor: "country", sortable: false},
    {label: "Last Login", accessor: "last_login", sortable: true},
    {label: "Disc. Ver.", accessor: "ver", sortable: true},
    {label: "Disc. Device", accessor: "device", sortable: false}
  ];

  const [tableData, handleSorting, setTableData] = useSortableTable(filteredUsers, columns);

  useEffect(() => {
    setTableData(filteredUsers)
  }, [filteredUsers]);

  useEffect(() => {
    filterUsers();
  }, [deChecked, dfChecked, searchItem, countryFilter]);

  const handleSearchInputChange = (e) => {
    setSearchItem(e.target.value)
  }

  const handleCountryChange = (e) => {
    setCountryFilter(e.target.value)
  }

  const handleDeCheckboxChange = () => {
    setDeChecked(!deChecked);
  }

  const handleDfCheckboxChange = () => {
    setDfChecked(!dfChecked);
  }

  const filterUsers = () => {
    let filteredItems = deUsers.filter((user) =>
        user.email.toLowerCase().includes(searchItem.toLowerCase())
        || user.name.toLowerCase().includes(searchItem.toLowerCase())
        || (user.country != null && user.country.toLowerCase().includes(
            searchItem.toLowerCase()))
    );
    if (deChecked) {
      filteredItems = filteredItems.filter((user) =>
          user.deAppData.uuid !== null
      );
    }

    if (dfChecked) {
      filteredItems = filteredItems.filter((user) =>
          user.dfAppData.active === true
      );
    }

    if (countryFilter !== "") {
      filteredItems = filteredItems.filter(
          (user) => user.country === countryFilter);
    }
    setFilteredUsers(filteredItems);
  }

  useEffect(() => {
    fetchUsers(instance).then((response) => {
      let users = response.data
      .map((user) => mapUsers(user));
      setDeUsers(users);
      setFilteredUsers(users)
    });
  }, [instance]);

  function mapUsers(user) {
    user.statusColor = {
      de: getStatusColors(
          user.deAppData.uuid === null ? null : user.deAppData.active),
      df: getStatusColors(user.dfAppData.active ? true : null)
    }

    user.last_login = user.deAppData.lastLogin ? new Date(user.deAppData.lastLogin) : null;
    user.ver = user.deAppData.applicationVersion;

    return user;
  }

  function getStatusColors(status) {
    let primaryColor = '#BFBFBF';
    let secondaryColor = '#8C8C8C';
    if (status === true) {
      primaryColor = '#29CB41';
      secondaryColor = '#1D912F';
    } else if (status === false) {
      primaryColor = '#FF5F57';
      secondaryColor = '#E1473F';
    }
    return {
      primary: primaryColor,
      secondary: secondaryColor,
    };
  }

  function showAddUserButton() {

    if (hasWritePermission(instance)) {
      return (
          <AddUserButton instance={instance}/>
      );
    } else {
      return (<></>);
    }
  }

  return (
      <>
        {/* Dashboard actions */}
        <div className="sm:flex sm:justify-between sm:items-center mb-8">
          <h1 className="mb-4 text-3xl font-extrabold text-gray-900 dark:text-white md:text-5xl lg:text-6xl"><span
              className="text-transparent bg-clip-text bg-gradient-to-r to-emerald-600 from-cyan-700">Users Dashboard</span></h1>
          {/*<div className="text-left uppercase text-4xl un">Users Dashboard</div>*/}
          {/* Left: Avatars */}
          <DashboardAvatars/>

          {/* Right: Actions */}
          <div
              className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
            {/* Search form */}
            <form className="border-b border-slate-200 dark:border-slate-700">
              <div className="relative">
                <label htmlFor="userSearch" className="sr-only">
                  Search
                </label>
                <input
                    id="userSearch"
                    className="w-full dark:text-slate-300 bg-white dark:bg-slate-800 border-0 focus:ring-transparent placeholder-slate-400 dark:placeholder-slate-500 appearance-none py-3 pl-10 pr-4"
                    type="search"
                    placeholder="Search Users…"
                    value={searchItem}
                    onChange={handleSearchInputChange}
                    // ref={searchInput}
                />
                <button className="absolute inset-0 right-auto group"
                        type="submit" aria-label="Search">
                  <svg
                      className="w-4 h-4 shrink-0 fill-current text-slate-400 dark:text-slate-500 group-hover:text-slate-500 dark:group-hover:text-slate-400 ml-4 mr-2"
                      viewBox="0 0 16 16"
                      xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                        d="M7 14c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zM7 2C4.243 2 2 4.243 2 7s2.243 5 5 5 5-2.243 5-5-2.243-5-5-5z"/>
                    <path
                        d="M15.707 14.293L13.314 11.9a8.019 8.019 0 01-1.414 1.414l2.393 2.393a.997.997 0 001.414 0 .999.999 0 000-1.414z"/>
                  </svg>
                </button>
              </div>
            </form>
            <form className="max-w-sm mx-auto">
              <label htmlFor="countries"
                     className="sr-only">Select country</label>
              <select id="countries"
                      onChange={handleCountryChange}
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-3 pr-10 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <CountriesDropdownValues/>
              </select>
            </form>
            {/* Filter button */}
            <ul className="mb-0">
              <li className="py-1 px-3">
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox"
                         checked={deChecked}
                         onChange={() => handleDeCheckboxChange()}/>
                  <span
                      className="text-sm font-medium ml-2">DE Users</span>
                </label>
              </li>
              <li className="py-1 px-3">
                <label className="flex items-center">
                  <input type="checkbox" className="form-checkbox"
                         checked={dfChecked}
                         onChange={() => handleDfCheckboxChange()}/>
                  <span
                      className="text-sm font-medium ml-2">DF Users</span>
                </label>
              </li>
            </ul>
            {showAddUserButton()}
          </div>

        </div>
        <div
            className="col-span-full xl:col-span-12 bg-white dark:bg-slate-800 shadow-lg rounded-sm border border-slate-200 dark:border-slate-700">
          <header
              className="px-5 py-4 border-b border-slate-100 dark:border-slate-700">
            <h2 className="font-semibold text-slate-800 dark:text-slate-100">Users (filtered/total) {filteredUsers.length}/{deUsers.length}</h2>
          </header>

          {/* Table */}
          <div className="overflow-x-auto text-xs">
            <table className="table-auto w-full">
              {/* Table header */}
              <UsersTableHead {...{columns, handleSorting}} />
              {/* Table body */}
              <UsersTableBody {...{tableData, instance}} />
            </table>

          </div>

        </div>
      </>
  );
}

export default UsersList;
