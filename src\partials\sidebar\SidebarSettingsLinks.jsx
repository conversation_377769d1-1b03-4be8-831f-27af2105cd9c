import React from 'react';
import SidebarLinkGroup from '../SidebarLinkGroup.jsx';
import SidebarNavLink from './SidebarNavLink.jsx';

function SidebarSettingsLinks({pathname, sidebarExpanded, setSidebarExpanded}) {

  return (
    <SidebarLinkGroup activecondition={pathname.includes('settings')}>
      {(handleClick, open) => {
        return (
          <React.Fragment>
            <a
              href="#"
              className={`block text-slate-200 truncate transition duration-150 ${
                pathname.includes('settings') ? 'hover:text-slate-200' : 'hover:text-white'
              }`}
              onClick={(e) => {
                e.preventDefault();
                sidebarExpanded ? handleClick() : setSidebarExpanded(true);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                    <path
                      className={`fill-current ${pathname.includes('settings') ? 'text-indigo-500' : 'text-slate-600'}`}
                      d="M19.714 14.7l-7.007 7.007-1.414-1.414 7.007-7.007c-.195-.4-.298-.84-.3-1.286a3 3 0 113 3 2.969 2.969 0 01-1.286-.3z"
                    />
                    <path
                      className={`fill-current ${pathname.includes('settings') ? 'text-indigo-300' : 'text-slate-400'}`}
                      d="M10.714 18.3c.4-.195.84-.298 1.286-.3a3 3 0 11-3 3c.002-.446.105-.885.3-1.286l-6.007-6.007 1.414-1.414 6.007 6.007z"
                    />
                    <path
                      className={`fill-current ${pathname.includes('settings') ? 'text-indigo-500' : 'text-slate-600'}`}
                      d="M5.7 10.714c.195.4.298.84.3 1.286a3 3 0 11-3-3c.446.002.885.105 1.286.3l7.007-7.007 1.414 1.414L5.7 10.714z"
                    />
                    <path
                      className={`fill-current ${pathname.includes('settings') ? 'text-indigo-300' : 'text-slate-400'}`}
                      d="M19.707 9.292a3.012 3.012 0 00-1.415 1.415L13.286 5.7c-.4.195-.84.298-1.286.3a3 3 0 113-3 2.969 2.969 0 01-.3 1.286l5.007 5.006z"
                    />
                  </svg>
                  <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                              Settings
                            </span>
                </div>
                {/* Icon */}
                <div className="flex shrink-0 ml-2">
                  <svg className={`w-3 h-3 shrink-0 ml-1 fill-current text-slate-400 ${open && 'rotate-180'}`} viewBox="0 0 12 12">
                    <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
                  </svg>
                </div>
              </div>
            </a>
            <div className="lg:hidden lg:sidebar-expanded:block 2xl:block">
              <ul className={`pl-9 mt-1 ${!open && 'hidden'}`}>
                <SidebarNavLink path="/settings/account" label="My Account"></SidebarNavLink>
                <SidebarNavLink path="/settings/notifications" label="My Notifications"></SidebarNavLink>
                <SidebarNavLink path="/settings/apps" label="Connected Apps"></SidebarNavLink>
                <SidebarNavLink path="/settings/plans" label="Plans"></SidebarNavLink>
                <SidebarNavLink path="/settings/billing" label="Billing & Invoices"></SidebarNavLink>
                <SidebarNavLink path="/settings/feedback" label="Give Feedback"></SidebarNavLink>
                <SidebarNavLink path="/settings/notifications" label="My Notifications"></SidebarNavLink>
              </ul>
            </div>
          </React.Fragment>
        );
      }}
    </SidebarLinkGroup>
  );
}

export default SidebarSettingsLinks;