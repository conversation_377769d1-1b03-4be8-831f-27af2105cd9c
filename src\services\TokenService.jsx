import {InteractionRequiredAuthError} from '@azure/msal-browser';

export const getToken = async (msalInstance) => {
  // Default to using the first account if no account is active on page load
  if (!msalInstance.getActiveAccount() && msalInstance.getAllAccounts().length > 0) {
    // Account selection logic is app dependent. Adjust as needed for different use cases.
    msalInstance.setActiveAccount(msalInstance.getAllAccounts()[0]);
  }

  return msalInstance.acquireTokenSilent({
    scopes: [".default"],
  }).then(tokenResponse => {
    return tokenResponse.idToken;
  }).catch(async (error) => {
    if (error instanceof InteractionRequiredAuthError) {
      // fallback to interaction when silent call fails
      console.error("AUTH ERROR")
      return msalInstance.acquireTokenPopup({scopes: [".default"]});
    }
  });
}

export const getRoles = (msalInstance) => {
  return msalInstance.getActiveAccount().idTokenClaims.roles.map(r => r
  .replace("DairyInteligenAdminServices_User_", "")
  .replace("_Dev", "")
  .replace("_Stage", "")
  .replace("_Prod", "")
  .toUpperCase());
}

export const hasWritePermission = (msalInstance) => {
  return getRoles(msalInstance).includes("WRITE");
}



