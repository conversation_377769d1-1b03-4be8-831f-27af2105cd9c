import {useMsal} from '@azure/msal-react';
import {loginRequest} from '../config/authConfig.js';

function LoginButton() {
  const {instance} = useMsal();

  const handleLoginPopup = () => {
    /**
     * When using popup and silent APIs, we recommend setting the redirectUri to a blank page or a page
     * that does not implement MSAL. Keep in mind that all redirect routes must be registered with the application
     * For more information, please follow this link: https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-browser/docs/login-user.md#redirecturi-considerations
     */
    instance
      .loginPopup({
        ...loginRequest,
        redirectUri: '/login',
      })
      .catch((error) => console.log(error));
  };

  return (
    <div>
      <button className="btn bg-indigo-500 hover:bg-indigo-600 text-white" onClick={handleLoginPopup}>
        <svg fill="#FFFFFF"
             width="20px" height="20px" viewBox="0 0 483.955 483.955" className="mr-1">
          <g>
            <g>
              <path d="M210.318,386.562c-1.727,0-3.453-0.445-5-1.34c-3.094-1.785-5-5.088-5-8.66v-75.201H10c-5.523,0-10-4.479-10-10v-98.769
                        c0-5.522,4.477-10,10-10h190.318v-75.201c0-3.572,1.906-6.874,5-8.66c3.094-1.787,6.906-1.787,10,0l233.109,134.585
                        c3.095,1.786,5,5.088,5,8.66c0,3.572-1.905,6.875-5,8.66L215.318,385.223C213.771,386.117,212.045,386.562,210.318,386.562z
                         M20,281.361h190.318c5.523,0,10,4.478,10,10v67.881l203.109-117.266L220.318,124.713v67.881c0,5.522-4.477,10-10,10H20V281.361z"
              />
            </g>
            <g>
              <path d="M462.479,468.289H325.624c-5.523,0-10-4.479-10-10s4.477-10,10-10h136.854c0.814,0,1.477-0.662,1.477-1.477V37.143
                        c0-0.814-0.662-1.477-1.477-1.477H325.624c-5.523,0-10-4.478-10-10s4.477-10,10-10h136.854c11.843,0,21.477,9.635,21.477,21.477
                        v409.669C483.956,458.654,474.321,468.289,462.479,468.289z"/>
            </g>
          </g>
        </svg>
        <span className="hidden xs:block ml-2">Sign In</span>
      </button>
    </div>
  );
}

export default LoginButton;
