import React from 'react';
import {NavLink} from 'react-router-dom';
import SidebarLinkGroup from '../SidebarLinkGroup.jsx';

function SidebarDashboardLinks({pathname, sidebarExpanded, setSidebarExpanded}) {
  const linkStyle = (style1, style2) =>
    pathname === '/' || pathname.includes('dashboard') ? style1 : style2;

  const linkActiveClass = ({isActive}) =>
    'block transition duration-150 truncate ' + (isActive ? 'text-indigo-500' : 'text-slate-400 hover:text-slate-200');


  return (
    <SidebarLinkGroup activecondition={pathname === '/' || pathname.includes('dashboard')}>
      {(handleClick, open) => {
        return (
          <React.Fragment>
            <a
              href="#"
              className={`block text-slate-200 truncate transition duration-150 ${
                linkStyle('hover:text-slate-200', 'hover:text-white')
              }`}
              onClick={(e) => {
                e.preventDefault();
                sidebarExpanded ? handleClick() : setSidebarExpanded(true);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                    <path
                      className={`fill-current ${
                        linkStyle('text-indigo-500', 'text-slate-400')
                      }`}
                      d="M12 0C5.383 0 0 5.383 0 12s5.383 12 12 12 12-5.383 12-12S18.617 0 12 0z"
                    />
                    <path
                      className={`fill-current ${
                        linkStyle('text-indigo-600', 'text-slate-600')
                      }`}
                      d="M12 3c-4.963 0-9 4.037-9 9s4.037 9 9 9 9-4.037 9-9-4.037-9-9-9z"
                    />
                    <path
                      className={`fill-current ${
                        linkStyle('text-indigo-200', 'text-slate-400')
                      }`}
                      d="M12 15c-1.654 0-3-1.346-3-3 0-.462.113-.894.3-1.285L6 6l4.714 3.301A2.973 2.973 0 0112 9c1.654 0 3 1.346 3 3s-1.346 3-3 3z"
                    />
                  </svg>
                  <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                              Dashboard
                            </span>
                </div>
                {/* Icon */}
                <div className="flex shrink-0 ml-2">
                  <svg className={`w-3 h-3 shrink-0 ml-1 fill-current text-slate-400 ${open && 'rotate-180'}`} viewBox="0 0 12 12">
                    <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
                  </svg>
                </div>
              </div>
            </a>
            <div className="lg:hidden lg:sidebar-expanded:block 2xl:block">
              <ul className={`pl-9 mt-1 ${!open && 'hidden'}`}>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Main
                              </span>
                  </NavLink>
                </li>
                {/*<li className="mb-1 last:mb-0">*/}
                {/*  <NavLink*/}
                {/*    end*/}
                {/*    to="/dashboard/analytics"*/}
                {/*    className={linkActiveClass}*/}
                {/*  >*/}
                {/*              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">*/}
                {/*                Analytics*/}
                {/*              </span>*/}
                {/*  </NavLink>*/}
                {/*</li>*/}
                {/*<li className="mb-1 last:mb-0">*/}
                {/*  <NavLink*/}
                {/*    end*/}
                {/*    to="/dashboard/fintech"*/}
                {/*    className={linkActiveClass}*/}
                {/*  >*/}
                {/*              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">*/}
                {/*                Fintech*/}
                {/*              </span>*/}
                {/*  </NavLink>*/}
                {/*</li>*/}
              </ul>
            </div>
          </React.Fragment>
        );
      }}
    </SidebarLinkGroup>
  );
}

export default SidebarDashboardLinks;