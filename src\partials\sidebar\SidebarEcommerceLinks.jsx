import React from 'react';
import {NavLink} from 'react-router-dom';
import SidebarLinkGroup from '../SidebarLinkGroup.jsx';

function SidebarEcommerceLinks({pathname, sidebarExpanded, setSidebarExpanded}) {

  const linkActiveClass = ({isActive}) =>
    'block transition duration-150 truncate ' + (isActive ? 'text-indigo-500' : 'text-slate-400 hover:text-slate-200');

  return (
    <SidebarLinkGroup activecondition={pathname.includes('ecommerce')}>
    {(handleClick, open) => {
      return (
        <React.Fragment>
          <a
            href="#"
            className={`block text-slate-200 truncate transition duration-150 ${
              pathname.includes('ecommerce') ? 'hover:text-slate-200' : 'hover:text-white'
            }`}
            onClick={(e) => {
              e.preventDefault();
              sidebarExpanded ? handleClick() : setSidebarExpanded(true);
            }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                  <path
                    className={`fill-current ${pathname.includes('ecommerce') ? 'text-indigo-300' : 'text-slate-400'}`}
                    d="M13 15l11-7L11.504.136a1 1 0 00-1.019.007L0 7l13 8z"
                  />
                  <path
                    className={`fill-current ${pathname.includes('ecommerce') ? 'text-indigo-600' : 'text-slate-700'}`}
                    d="M13 15L0 7v9c0 .355.189.685.496.864L13 24v-9z"
                  />
                  <path
                    className={`fill-current ${pathname.includes('ecommerce') ? 'text-indigo-500' : 'text-slate-600'}`}
                    d="M13 15.047V24l10.573-7.181A.999.999 0 0024 16V8l-11 7.047z"
                  />
                </svg>
                <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                              E-Commerce
                            </span>
              </div>
              {/* Icon */}
              <div className="flex shrink-0 ml-2">
                <svg className={`w-3 h-3 shrink-0 ml-1 fill-current text-slate-400 ${open && 'rotate-180'}`} viewBox="0 0 12 12">
                  <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
                </svg>
              </div>
            </div>
          </a>
          <div className="lg:hidden lg:sidebar-expanded:block 2xl:block">
            <ul className={`pl-9 mt-1 ${!open && 'hidden'}`}>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/customers"
                  className={({ isActive }) =>
                    'block transition duration-150 truncate ' + (isActive ? 'text-indigo-500' : 'text-slate-400 hover:text-slate-200')
                  }
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Customers
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/orders"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Orders
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/invoices"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Invoices
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/shop"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Shop
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/shop-2"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Shop 2
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/product"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Single Product
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/cart"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Cart
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/cart-2"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Cart 2
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/cart-3"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Cart 3
                              </span>
                </NavLink>
              </li>
              <li className="mb-1 last:mb-0">
                <NavLink
                  end
                  to="/ecommerce/pay"
                  className={linkActiveClass}
                >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Pay
                              </span>
                </NavLink>
              </li>
            </ul>
          </div>
        </React.Fragment>
      );
    }}
  </SidebarLinkGroup>
  );
}

export default SidebarEcommerceLinks;