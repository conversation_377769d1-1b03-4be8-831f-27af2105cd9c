import React from 'react';
import { NavLink } from 'react-router-dom';

function SidebarNotificationLinks({ pathname }) {
  return (
    <li className={`px-3 py-2 rounded-sm mb-0.5 last:mb-0 ${pathname.includes('notifications') && 'bg-slate-900'}`}>
      <NavLink
        end
        to="/notifications"
        className={`block text-slate-200 truncate transition duration-150 ${
          pathname.includes('notifications') ? 'hover:text-slate-200' : 'hover:text-white'
        }`}
      >
        <div className="flex items-center">
          <svg className="shrink-0 h-6 w-6" viewBox="0 0 24 24">
            <path
              className={`fill-current ${
                pathname.includes('notifications') ? 'text-indigo-500' : 'text-slate-400'
              }`}
              d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"
            />
          </svg>
          <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
            Notifications
          </span>
        </div>
      </NavLink>
    </li>
  );
}

export default SidebarNotificationLinks;
