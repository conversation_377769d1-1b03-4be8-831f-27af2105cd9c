import {createClaimsTable} from '../utils/ClaimUtils.js';

export const IdTokenData = (props) => {
  const tokenClaims = createClaimsTable(props.idTokenClaims);

  const tableRow = Object.keys(tokenClaims).map((key, index) => {
    return (
      <tr key={key}>
        {tokenClaims[key].map((claimItem) => (
          <td className="border px-4 py-2 break-all" key={claimItem}>{claimItem}</td>
        ))}
      </tr>
    );
  });

  return (

    <div>
      <p>
        See below the claims in your <strong> ID token </strong>. For more information, visit:{' '}
        <span>
          <a href="https://docs.microsoft.com/en-us/azure/active-directory/develop/id-tokens#claims-in-an-id-token">docs.microsoft.com</a>
        </span>
      </p>
      <div>

        {/* Table */}
        <div>
          <table className="table-auto">
            <thead>
            <tr>
              <th className="px-4 py-2">Claim</th>
              <th className="px-4 py-2">Value</th>
              <th className="px-4 py-2">Description</th>
            </tr>
            </thead>
            <tbody>
            <tr key="tokenRow">
              <td className="border px-4 py-2 break-all" key="tokenName">idToken</td>
              <td className="border px-4 py-2 break-all" key="tokenData">{props.token}</td>
              <td className="border px-4 py-2 break-all" key="tokenDesc"></td>
            </tr>
            {tableRow}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
