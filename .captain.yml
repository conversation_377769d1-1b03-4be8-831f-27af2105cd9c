kind: kubernetes
app: dairyenteligenadminservicesui
type: web
team: dairyenteligenadminservices
docker_image: dairy-enteligen-admin-ui-app
internal: false
replicas: 1
auto_scaling:
  max_replicas: 2
  cpu_target_average_utilization: 80
container_port: 9000
healthcheck:
  path: /health
  initialDelaySeconds: 30
  timeoutSeconds: 120
  periodSeconds: 120
  failureThreshold: 10
revisionHistoryLimit: 10
dns:
  zone: cglcloud
  name: de-admin
configs: #(Required)
  - name: nginx-local #(Required)
    type: empty-volume #(Required)
    mountPath: /var/cache/nginx/ #(Required)
    containers: ["dairyenteligenadminservicesui"] #(Required): Change this to be the same as app name
envvars:
  dev:
    REACT_APP_NODE_ENV: "dev"
  stage:
    REACT_APP_NODE_ENV: "stage"
  prod:
    REACT_APP_NODE_ENV: "prod"
headers:
  csp:
    default: # dev, stage, prod, or default
      script-src:
        self: true
        unsafe-inline: true
      style-src:
        self: true
        unsafe-inline: true
        sources:
          - https://fonts.googleapis.com
      img-src:
        self: true
        sources:
          - https://www.w3.org
          - https://www.w3.org/2000/svg
      manifest-src:
        self: true
        sources:
          - https://de-admin.dev.cglcloud.in/
          - https://de-admin.stage.cglcloud.in/
          - https://de-admin.cglcloud.com/
      connect-src:
        self: true
        sources:
          - https://de-admin.dev.cglcloud.in
          - https://login.microsoftonline.com
          - https://api-dev.dev.dev-cglcloud.com
          - https://de-admin.stage.cglcloud.in
          - https://api-stage.stage.cglcloud.in
          - https://de-admin.cglcloud.com
          - https://api.cglcloud.com
      font-src:
        self: true
        sources:
          - https://fonts.gstatic.com
