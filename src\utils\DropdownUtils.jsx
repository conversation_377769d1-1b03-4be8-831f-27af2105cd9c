export const closeOnClickOutside = (trigger, dropdown, dropdownOpen, setDropdownOpen) => {
  const clickHandler = ({target}) => {
    if (!dropdown.current) return;
    if (!dropdownOpen || dropdown.current.contains(target) || trigger.current.contains(target)) return;
    setDropdownOpen(false);
  };
  document.addEventListener('click', clickHandler);
  return () => document.removeEventListener('click', clickHandler);
}

export const closeOnEscKey = (dropdownOpen, setDropdownOpen) => {
  const keyHandler = ({ keyCode }) => {
    if (!dropdownOpen || keyCode !== 27) return;
    setDropdownOpen(false);
  };
  document.addEventListener('keydown', keyHandler);
  return () => document.removeEventListener('keydown', keyHandler);
}
