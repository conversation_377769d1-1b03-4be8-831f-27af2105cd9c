import React from "react";

export const CountriesDropdownValues = (props) => {
  return (
      <>
        <option defaultValue="" value="">Select Country</option>
        <option value="Brazil">Brazil</option>
        <option value="Canada">Canada</option>
        <option value="Italy">Italy</option>
        <option value="India">India</option>
        <option value="US">United States</option>
        <option disabled>──────────</option>
        <option value="Argentina">Argentina</option>
        <option value="China">China</option>
        <option value="France">France</option>
        <option value="Hungary">Hungary</option>
        <option value="Korea">Korea</option>
        <option value="Mexico">Mexico</option>
        <option value="Netherlands">Netherlands</option>
        <option value="Pakistan">Pakistan</option>
        <option value="Philippines">Philippines</option>
        <option value="Poland">Poland</option>
        <option value="Portugal">Portugal</option>
        <option value="Russia">Russia</option>
        <option value="Spain">Spain</option>
        <option value="South Africa">South Africa</option>
        <option value="Ukraine">Ukraine</option>
        <option value="United Kingdom">United Kingdom</option>
        <option value="Vietnam">Vietnam</option>
      </>
  );
}