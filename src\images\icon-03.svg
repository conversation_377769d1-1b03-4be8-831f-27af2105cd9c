<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="icon3-b">
            <stop stop-color="#E2E8F0" offset="0%" />
            <stop stop-color="#94A3B8" offset="100%" />
        </linearGradient>
        <linearGradient x1="50%" y1="24.537%" x2="50%" y2="99.142%" id="icon3-c">
            <stop stop-color="#334155" offset="0%" />
            <stop stop-color="#334155" stop-opacity="0" offset="100%" />
        </linearGradient>
        <path id="icon3-a" d="M16 0l16 32-16-5-16 5z" />
    </defs>
    <g transform="rotate(90 16 16)" fill="none" fill-rule="evenodd">
        <mask id="icon3-d" fill="#fff">
            <use xlink:href="#icon3-a" />
        </mask>
        <use fill="url(#icon3-b)" xlink:href="#icon3-a" />
        <path fill="url(#icon3-c)" mask="url(#icon3-d)" d="M16-6h20v38H16z" />
    </g>
</svg>