import {Button} from "flowbite-react";
import {AddUserModal} from "./AddUserModal.jsx";
import React, {useState} from "react";

export const AddUserButton = (props) => {
  const [openModal, setOpenModal] = useState(false);

  return (
      <>
        {/* Add user button */}
        <Button className="inline-block align-baseline"
                onClick={() => setOpenModal(true)}>Add User</Button>
        <AddUserModal setOpenModal={setOpenModal} openModal={openModal} instance={props.instance}/>
      </>
  );
}