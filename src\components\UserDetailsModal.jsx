import {Mo<PERSON>, Spinner, ToggleSwitch} from "flowbite-react";
import React, {useEffect, useState} from "react";
import {addUser, deleteDfUser, getUserDetails, updateDeAppUser} from "../services/UsersService.jsx";
import {instantToSimpleDate} from "../utils/TimeUtils.jsx";
import {hasWritePermission} from "../services/TokenService.jsx";

export const UserDetailsModal = (props) => {
  const [userData, setUserData] = useState({
    uuid: null,
    email: null,
    deAppData: {
      uuid: null,
      active: false,
      lastLogin: null,
      deviceType: null,
      deviceModel: null,
      applicationVersion: null,
      firstVisit: null
    },
    dfAppData: {
      active: false,
      email: null
    },
    name: null,
    country: null
  });

  const [deUserCheckbox, setDeUserCheckbox] = useState(false);
  const [dfUserCheckbox, setDfUserCheckbox] = useState(false);

  useEffect(() => {
    if(!props.selectedUser.deAppData.uuid && !props.selectedUser.email) {
      return;
    }
    getUserDetails(props.instance, props.selectedUser.deAppData.uuid, props.selectedUser.email).then((response) => {
      setUserData(response.data);
      setDeUserCheckbox(response.data.deAppData.active);
      setDfUserCheckbox(response.data.dfAppData.active);
      props.setUserLoading(false);
    });
  }, [props.instance, props.selectedUser]);

  function hiddenClass(conditional) {
    if(conditional) return 'hidden';
    else return '';
  }

  function deUserCheckboxChangeHandler(newState) {
    if(userData.deAppData.uuid == null) {
      // new user
      addUser(props.instance, {
        email: userData.email,
        fullName: userData.name,
        country: userData.country,
        deUser: true,
        dfUser: false,
      })
      .then((response) => {
        let updatedUserData = copyUserData(userData);
        updatedUserData.deAppData = response.data.deAppData;
        setUserData(updatedUserData);
        setDeUserCheckbox(newState);
      }).catch((e) => {
        console.log('Error creating DE App user', e);
      });
    } else {
      //existing user
      updateDeAppUser(props.instance, userData.deAppData.uuid, {active: newState}).then(response => {
        let updatedUserData = copyUserData(userData);
        updatedUserData.deAppData = response.data.deAppData;
        setUserData(updatedUserData);
        setDeUserCheckbox(newState);
      });
    }
  }

  function dfUserCheckboxChangeHandler(newState) {
    if(newState) {
      // create user
      addUser(props.instance, {
        email: userData.email,
        fullName: userData.name,
        country: userData.country,
        deUser: false,
        dfUser: true,
      })
      .then((response) => {
        let updatedUserData = copyUserData(userData);
        updatedUserData.dfAppData = response.data.dfAppData;
        setUserData(updatedUserData);
        setDfUserCheckbox(newState);
      }).catch((e) => {
        console.log('Error creating DF App user', e);
      });
    } else {
      // delete user
      deleteDfUser(props.instance, userData.dfAppData.email).then(r => {
        let updatedUserData = copyUserData(userData);
        updatedUserData.dfAppData = {email: null, active: false};
        setUserData(updatedUserData);
        setDfUserCheckbox(newState);
      })
    }
  }

  function copyUserData(currentUser) {
    return {
      uuid: currentUser.uuid,
      email: currentUser.email,
      name: currentUser.name,
      country: currentUser.country,
      deAppData: {
        uuid: currentUser.deAppData.uuid,
        active: currentUser.deAppData.active,
        lastLogin: currentUser.deAppData.lastLogin,
        deviceType: currentUser.deAppData.deviceType,
        deviceModel: currentUser.deAppData.deviceModel,
        applicationVersion: currentUser.deAppData.applicationVersion,
        firstVisit: currentUser.deAppData.firstVisit,
      },
      dfAppData: {
        active: currentUser.dfAppData.active,
        email: currentUser.dfAppData.email,
      }
    }
  }

  return (
      <>
        <Modal show={props.openModal} onClose={() => props.setOpenModal(false)} size="7xl">
          <Modal.Header>User Details - {props.selectedUser.email}</Modal.Header>
          <Modal.Body>
            <div className="text-center">
              <div className={hiddenClass(!props.userLoading)}>
                <Spinner aria-label="Loading user details ..." size="xl"/>
              </div>
              <div className={hiddenClass(props.userLoading)}>
                <div className="h-56 grid grid-cols-3 gap-4 content-start">
                  <div className="border-2 rounded-lg border-gray-300 text-left p-2 bg-orange-100">
                    <div><span className="font-bold">User Name:</span> {userData.name}</div>
                    <div><span className="font-bold">User Email:</span> {userData.email}</div>
                    <div><span className="font-bold">Country:</span> {userData.country}</div>
                  </div>
                  <div className="border-2 rounded-lg border-gray-300 text-left p-2 bg-lime-100">
                      <ToggleSwitch className="p-2" disabled={!hasWritePermission(props.instance)} name="deUser" checked={deUserCheckbox} onChange={deUserCheckboxChangeHandler} label="Dairy Discover"/>
                      <ToggleSwitch className="p-2" disabled={!hasWritePermission(props.instance)} name="dfUser" checked={dfUserCheckbox} onChange={dfUserCheckboxChangeHandler} label="Dairy Forecast"/>
                  </div>
                  <div className="border-2 rounded-lg border-gray-300 text-left p-2 bg-cyan-100">
                    <div><span className="font-bold">Last Login:</span> {instantToSimpleDate(new Date(userData.deAppData.lastLogin))}</div>
                    <div><span className="font-bold">App Version:</span> {userData.deAppData.applicationVersion}</div>
                    <div><span className="font-bold">Device:</span> {userData.deAppData.deviceType} / {userData.deAppData.deviceModel}</div>
                    <div><span className="font-bold">First Visit:</span> {instantToSimpleDate(new Date(userData.deAppData.firstVisit))}</div>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>
        </Modal>
      </>
  );
}
