import React, { useState, useEffect } from 'react';
import { CountriesDropdownValues } from './CountriesDropdownValues.jsx';

function NotificationSetup({ notification, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    title: '',
    type: 'Release Notes',
    description: '',
    userSegmentation: 'Internal',
    country: '',
    startDate: '',
    endDate: '',
    attachments: []
  });

  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (notification) {
      setFormData({
        title: notification.title || '',
        type: notification.type || 'Release Notes',
        description: notification.description || '',
        userSegmentation: notification.userSegmentation || 'Internal',
        country: notification.country || '',
        startDate: notification.duration?.startDate || '',
        endDate: notification.duration?.endDate || '',
        attachments: notification.attachments || []
      });
    }
  }, [notification]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    // TODO: /api/notifications/upload - Upload files to server
    const newAttachments = files.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file) // Temporary URL for preview
    }));
    
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments]
    }));
  };

  const handleRemoveAttachment = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.country) {
      newErrors.country = 'Country selection is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // TODO: /api/analytics/track - Track notification creation/edit events
    const notificationData = {
      ...formData,
      duration: {
        startDate: formData.startDate,
        endDate: formData.endDate
      },
      status: 'Published' // Published status when using submit button
    };

    onSave(notificationData);
  };

  const handlePreview = () => {
    // TODO: /api/notifications/preview - Generate preview
    console.log('Preview notification:', formData);
  };

  const handleDraft = () => {
    if (!validateForm()) {
      return;
    }

    const draftData = {
      ...formData,
      duration: {
        startDate: formData.startDate,
        endDate: formData.endDate
      },
      status: 'Draft'
    };

    onSave(draftData);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      {/* Header */}
      <div className="sm:flex sm:justify-between sm:items-center mb-8">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl md:text-3xl text-slate-800 dark:text-slate-100 font-bold">
            {notification ? 'Edit Notification' : 'Notification Setup'}
          </h1>
        </div>

        {/* Actions */}
        <div className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
          <button
            type="button"
            className="btn border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 text-slate-600 dark:text-slate-300"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 text-slate-600 dark:text-slate-300"
            onClick={handlePreview}
          >
            Preview
          </button>
          <button
            type="button"
            className="btn bg-slate-500 hover:bg-slate-600 text-white"
            onClick={handleDraft}
          >
            Draft
          </button>
          <button
            type="submit"
            form="notification-form"
            className="btn bg-indigo-500 hover:bg-indigo-600 text-white"
          >
            Publish
          </button>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white dark:bg-slate-800 shadow-lg rounded-sm border border-slate-200 dark:border-slate-700">
        <div className="px-5 py-4">
          <form id="notification-form" onSubmit={handleSubmit} className="space-y-6">
            {/* Notification Type */}
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="type">
                Notification Type <span className="text-rose-500">*</span>
              </label>
              <div className="flex space-x-4">
                {['Release Notes', 'Special Action', 'Marketing Campaign'].map((type) => (
                  <label key={type} className="flex items-center">
                    <input
                      type="radio"
                      name="type"
                      value={type}
                      checked={formData.type === type}
                      onChange={handleInputChange}
                      className="form-radio"
                    />
                    <span className="text-sm ml-2">{type}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="title">
                Title <span className="text-rose-500">*</span>
              </label>
              <input
                id="title"
                name="title"
                className={`form-input w-full ${errors.title ? 'border-rose-300' : ''}`}
                type="text"
                placeholder="Enter your subject here"
                value={formData.title}
                onChange={handleInputChange}
              />
              {errors.title && <div className="text-xs mt-1 text-rose-500">{errors.title}</div>}
            </div>

            {/* Duration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="startDate">
                  Start Date
                </label>
                <input
                  id="startDate"
                  name="startDate"
                  className="form-input w-full"
                  type="date"
                  value={formData.startDate}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" htmlFor="endDate">
                  End Date
                </label>
                <input
                  id="endDate"
                  name="endDate"
                  className="form-input w-full"
                  type="date"
                  value={formData.endDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="description">
                Description <span className="text-rose-500">*</span>
              </label>
              <div className="border border-slate-200 dark:border-slate-700 rounded">
                {/* Toolbar */}
                <div className="flex items-center space-x-2 p-2 border-b border-slate-200 dark:border-slate-700">
                  <select className="text-sm border-0 bg-transparent">
                    <option>Normal text</option>
                  </select>
                  <div className="flex space-x-1">
                    <button type="button" className="p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 4v3H4a1 1 0 000 2h1v2a1 1 0 002 0V9h2a1 1 0 000-2H7V4a1 1 0 00-2 0zm7 0a1 1 0 012 0v9a1 1 0 01-2 0V4z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <button type="button" className="p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
                <textarea
                  id="description"
                  name="description"
                  className={`w-full border-0 focus:ring-0 resize-none ${errors.description ? 'border-rose-300' : ''}`}
                  rows="6"
                  placeholder="Description"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>
              {errors.description && <div className="text-xs mt-1 text-rose-500">{errors.description}</div>}
            </div>

            {/* User Segmentation */}
            <div>
              <label className="block text-sm font-medium mb-1">
                User Segmentation <span className="text-rose-500">*</span>
              </label>
              <div className="flex space-x-4">
                {['Internal', 'External'].map((segment) => (
                  <label key={segment} className="flex items-center">
                    <input
                      type="radio"
                      name="userSegmentation"
                      value={segment}
                      checked={formData.userSegmentation === segment}
                      onChange={handleInputChange}
                      className="form-radio"
                    />
                    <span className="text-sm ml-2">{segment}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Country */}
            <div>
              <label className="block text-sm font-medium mb-1" htmlFor="country">
                Country <span className="text-rose-500">*</span>
              </label>
              <select
                id="country"
                name="country"
                className={`form-select w-full ${errors.country ? 'border-rose-300' : ''}`}
                value={formData.country}
                onChange={handleInputChange}
              >
                <option value="">Select</option>
                <CountriesDropdownValues />
              </select>
              {errors.country && <div className="text-xs mt-1 text-rose-500">{errors.country}</div>}
            </div>

            {/* File Attachments */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Attachments
              </label>
              <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-6">
                <input
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                  id="file-upload"
                  accept=".pdf,.png,.jpg,.jpeg,.doc,.docx"
                />
                <label
                  htmlFor="file-upload"
                  className="cursor-pointer flex flex-col items-center justify-center"
                >
                  <svg className="w-8 h-8 text-slate-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    Click to upload or drag and drop
                  </span>
                  <span className="text-xs text-slate-500 dark:text-slate-500">
                    PDF, PNG, JPG up to 10MB
                  </span>
                </label>
              </div>

              {/* Uploaded Files */}
              {formData.attachments.length > 0 && (
                <div className="mt-4 space-y-2">
                  {formData.attachments.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-700 rounded">
                      <div className="flex items-center space-x-2">
                        <svg className="w-4 h-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-slate-600 dark:text-slate-300">{file.name}</span>
                        <span className="text-xs text-slate-500">({formatFileSize(file.size)})</span>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveAttachment(index)}
                        className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </form>
        </div>
      </div>
    </>
  );
}

export default NotificationSetup;
