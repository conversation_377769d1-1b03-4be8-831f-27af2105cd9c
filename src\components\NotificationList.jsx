import React, { useState, useEffect } from 'react';
import { useMsal } from '@azure/msal-react';

function NotificationList({ 
  notifications, 
  onCreateNotification, 
  onEditNotification, 
  onDeleteNotification, 
  onStatusChange 
}) {
  const { instance } = useMsal();
  const [filteredNotifications, setFilteredNotifications] = useState(notifications);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');

  // TODO: /api/notifications/search - Implement server-side search and filtering for better performance
  useEffect(() => {
    let filtered = notifications;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(notification => notification.status === statusFilter);
    }

    // Type filter
    if (typeFilter) {
      filtered = filtered.filter(notification => notification.type === typeFilter);
    }

    setFilteredNotifications(filtered);
  }, [notifications, searchTerm, statusFilter, typeFilter]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleStatusFilterChange = (e) => {
    setStatusFilter(e.target.value);
  };

  const handleTypeFilterChange = (e) => {
    setTypeFilter(e.target.value);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'Published':
        return 'bg-emerald-100 text-emerald-600 dark:bg-emerald-400/10 dark:text-emerald-400';
      case 'Draft':
        return 'bg-amber-100 text-amber-600 dark:bg-amber-400/10 dark:text-amber-400';
      default:
        return 'bg-slate-100 text-slate-600 dark:bg-slate-400/10 dark:text-slate-400';
    }
  };

  const handleStatusToggle = (notification) => {
    // TODO: /api/notifications/{id}/toggle-status - Toggle notification status via API
    const newStatus = notification.status === 'Published' ? 'Draft' : 'Published';
    onStatusChange(notification.id, newStatus);
  };

  return (
    <>
      {/* Header */}
      <div className="sm:flex sm:justify-between sm:items-center mb-8">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl md:text-3xl text-slate-800 dark:text-slate-100 font-bold">
            Notifications Management
          </h1>
        </div>

        {/* Actions */}
        <div className="grid grid-flow-col sm:auto-cols-max justify-start sm:justify-end gap-2">
          {/* Search */}
          <div className="relative">
            <label htmlFor="notification-search" className="sr-only">Search</label>
            <input
              id="notification-search"
              className="form-input w-full pl-9 focus:border-slate-300"
              type="search"
              placeholder="Search notifications…"
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <button className="absolute inset-0 right-auto group" type="submit" aria-label="Search">
              <svg className="w-4 h-4 shrink-0 fill-current text-slate-400 group-hover:text-slate-500 ml-3 mr-2" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 14c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7zM7 2C4.243 2 2 4.243 2 7s2.243 5 5 5 5-2.243 5-5-2.243-5-5-5z" />
                <path d="M15.707 14.293L13.314 11.9a8.019 8.019 0 01-1.414 1.414l2.393 2.393a.997.997 0 001.414 0 .999.999 0 000-1.414z" />
              </svg>
            </button>
          </div>

          {/* Status Filter */}
          <select
            className="form-select"
            value={statusFilter}
            onChange={handleStatusFilterChange}
          >
            <option value="">All Status</option>
            <option value="Draft">Draft</option>
            <option value="Published">Published</option>
          </select>

          {/* Type Filter */}
          <select
            className="form-select"
            value={typeFilter}
            onChange={handleTypeFilterChange}
          >
            <option value="">All Types</option>
            <option value="Release Notes">Release Notes</option>
            <option value="Special Action">Special Action</option>
            <option value="Marketing Campaign">Marketing Campaign</option>
          </select>

          {/* Create Notification Button */}
          <button
            className="btn bg-indigo-500 hover:bg-indigo-600 text-white"
            onClick={onCreateNotification}
          >
            <svg className="w-4 h-4 fill-current opacity-50 shrink-0" viewBox="0 0 16 16">
              <path d="M15 7H9V1c0-.6-.4-1-1-1S7 .4 7 1v6H1c-.6 0-1 .4-1 1s.4 1 1 1h6v6c0 .6.4 1 1 1s1-.4 1-1V9h6c.6 0 1-.4 1-1s-.4-1-1-1z" />
            </svg>
            <span className="hidden xs:block ml-2">Create Notification</span>
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white dark:bg-slate-800 shadow-lg rounded-sm border border-slate-200 dark:border-slate-700 relative">
        <header className="px-5 py-4 border-b border-slate-100 dark:border-slate-700">
          <h2 className="font-semibold text-slate-800 dark:text-slate-100">
            All Notifications 
            <span className="text-slate-400 dark:text-slate-500 font-medium ml-1">
              ({filteredNotifications.length})
            </span>
          </h2>
        </header>
        <div className="overflow-x-auto">
          <table className="table-auto w-full">
            {/* Table header */}
            <thead className="text-xs font-semibold uppercase text-slate-400 dark:text-slate-500 bg-slate-50 dark:bg-slate-700 dark:bg-opacity-50">
              <tr>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Title</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Status</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Last Updated By</div>
                </th>
                <th className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                  <div className="font-semibold text-left">Actions</div>
                </th>
              </tr>
            </thead>
            {/* Table body */}
            <tbody className="text-sm divide-y divide-slate-100 dark:divide-slate-700">
              {filteredNotifications.map((notification) => (
                <tr key={notification.id}>
                  <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="font-medium text-slate-800 dark:text-slate-100">
                          {notification.title}
                        </div>
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          {notification.type}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                    <div className={`inline-flex font-medium rounded-full text-center px-2.5 py-0.5 ${getStatusBadgeClass(notification.status)}`}>
                      {notification.status}
                    </div>
                  </td>
                  <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap">
                    <div className="text-left">
                      <div className="font-medium text-slate-800 dark:text-slate-100">
                        {notification.lastUpdatedBy}
                      </div>
                      <div className="text-xs text-slate-500 dark:text-slate-400">
                        {notification.lastUpdated}
                      </div>
                    </div>
                  </td>
                  <td className="px-2 first:pl-5 last:pr-5 py-3 whitespace-nowrap w-px">
                    <div className="flex items-center space-x-1">
                      <button
                        className="text-slate-400 hover:text-slate-500 dark:text-slate-500 dark:hover:text-slate-400"
                        onClick={() => onEditNotification(notification)}
                      >
                        <span className="sr-only">Edit</span>
                        <svg className="w-8 h-8 fill-current" viewBox="0 0 32 32">
                          <path d="m16 18.414-8-8L6.586 12 16 21.414 25.414 12 24 10.586z" />
                        </svg>
                      </button>
                      <button
                        className={`text-xs font-medium px-2 py-1 rounded ${
                          notification.status === 'Published' 
                            ? 'bg-amber-100 text-amber-600 hover:bg-amber-200' 
                            : 'bg-emerald-100 text-emerald-600 hover:bg-emerald-200'
                        }`}
                        onClick={() => handleStatusToggle(notification)}
                      >
                        {notification.status === 'Published' ? 'Unpublish' : 'Publish'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}

export default NotificationList;
