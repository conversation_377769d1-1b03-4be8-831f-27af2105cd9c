*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# No mac stuff
.DS_Store

# No vscode stuff
.vscode

# No bundled stuff in git
dist
lib

# No rollup stuff
.rts2_cache*
.cache

# No parcel stuff
.parcel-cache

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/
node_modules/*

# Typescript v1 declaration files
typings/

#WebStorm
.idea/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env



.travis/README.md
.travis/scripts

deployment_keys
deployment_keys-private
deployment_keys.tar

.github/.release
release-artifacts-*

.idea
*.iws
*.iml
*.ipr
