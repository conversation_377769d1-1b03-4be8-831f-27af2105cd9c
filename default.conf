server {
    listen       9000;
    server_name  localhost;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri /index.html;
        proxy_set_header Accept-Encoding "";
    }

    location /health {
        add_header Content-Type application/json;
        return 200 '{"project": "dairy-enteligen-admin-ui-app", "time": "${msec}"}';
    }

    client_body_timeout 120;
    client_header_timeout 120;
    keepalive_timeout 120;
    send_timeout 120;
    client_body_buffer_size 512K;
    client_header_buffer_size 512k;
    client_max_body_size 200m;
    large_client_header_buffers 4 512k;

    proxy_max_temp_file_size 0;
    gzip off;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied any expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/json;
    gzip_disable "MSIE [1-6]\.";
    expires off;
}