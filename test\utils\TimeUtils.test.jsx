import { expect, test } from 'vitest'
import {instantToSimpleDate} from "../../src/utils/TimeUtils.jsx";

test('Instant is correctly transformed to simple date format', () => {
  expect(instantToSimpleDate(new Date('2023-09-20T11:14:03.517560514Z'))).toBe('20 Sept 2023')
})

test('Instant is correctly transformed to simple date format', () => {
  expect(instantToSimpleDate(new Date('2024-09-11T13:18:40.544609689Z'))).toBe('11 Sept 2024')
})

test('Instant is correctly transformed to simple date format', () => {
  expect(instantToSimpleDate(new Date('2019-04-24T06:51:21.476Z'))).toBe('24 Apr 2019')
})
