version: "1"

stages:
  # [START build]
  build:
    steps:
      # Build the project
      - name: build-pr
        image: node:latest
        ruleset:
          event: [ pull_request ]
        commands:
          - npm install
          - npm run build

      - name: build-push
        image: node:latest
        ruleset:
          if:
            branch: [ dev, stage, main ]
            event: [ push, tag ]
        commands:
          - npm install
          - npm run build -- --mode $BUILD_BRANCH

      # SonarQube
      - name: sonar-qube-pr
        image: docker-utilities.binrepo.cglcloud.in/enablementtools/sonarqube-plugin:1-stable
        ruleset:
          event: [ pull_request ]
        parameters:
#          exclusions: src/test/**
          sonar_args:
#            - sonar.java.binaries=target/classes
#            - sonar.java.libraries=target/dependency
#            - sonar.java.source=21
#            - sonar.tests=src/test
          sources: ./src/
          team: dairyenteligenadminservices

      - name: sonar-qube-push
        image: docker-utilities.binrepo.cglcloud.in/enablementtools/sonarqube-plugin:1-stable
        ruleset:
          if:
            branch: [ dev ]
            event: [ push ]
        parameters:
#          exclusions: src/test/**,docs/**
#          sonar_args:
#            - sonar.java.binaries=target/classes
#            - sonar.java.libraries=target/dependency
#            - sonar.java.source=21
#            - sonar.tests=src/test
          sources: ./src/
          team: dairyenteligenadminservices

      # Build the Docker image for PR(s)
      - name: build-push-docker-pr
        image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
        ruleset:
          event: [ pull_request ]
        parameters:
          repo: dairyenteligen-services-ui
          skip_existing: true
          tags: '${VELA_BUILD_COMMIT:0:7}-pr'
      #
      #    - name: veracode-pipeline-scan
      #      image: openjdk:11.0.10-jdk-slim
      #      ruleset:
      #        event: [ pull_request ]
      #      secrets: [ veracode_user, veracode_pass ]
      #      commands:
      #        - apt-get -qq update && apt-get -qq --assume-yes install wget unzip
      #        - cp target/de-app-api.jar.original ${VELA_REPO_NAME}-veracode-${VELA_BUILD_NUMBER}.jar   # not scanning 3rd party libraries
      #        - wget https://downloads.veracode.com/securityscan/pipeline-scan-LATEST.zip
      #        - unzip pipeline-scan-LATEST.zip pipeline-scan.jar
      #        - java -jar pipeline-scan.jar
      #          --project_name "dairy-enteligen-admin-ui-app" --project_url "https://git.cglcloud.com/EMEA-Tech-Services/dairy-enteligen-admin-ui-app"
      #          --project_ref ${VELA_BUILD_COMMIT:0:7} --devment_stage "devment" --veracode_api_id $VERACODE_USER --veracode_api_key $VERACODE_PASS
      #          --fail_on_severity "Very High, High, Medium" --file ${VELA_REPO_NAME}-veracode-${VELA_BUILD_NUMBER}.jar --summary_display true --summary_output true --issue_details true
      #
      #    # Cleanup docker image created from PR
      #    - name: cleanup-pr-docker
      #      image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
      #      ruleset:
      #        if:
      #          event: [pull_request]
      #      parameters:
      #        delete: true
      #        repo: dairy-enteligen-admin-ui-app
      #        tags: '${VELA_BUILD_COMMIT:0:7}-pr'
      #
      # Build the Docker image for dev
      - name: build-push-docker-dev
        image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
        ruleset:
          if:
            branch: [dev]
            event: [push]
        parameters:
          repo: dairy-enteligen-admin-ui-app
          skip_existing: false
          tags:
            - 'latest-dev'
            - '${VELA_BUILD_COMMIT:0:7}-dev'

      # Build the Docker image for stage
      - name: build-push-docker-stage
        image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
        ruleset:
          if:
            branch: [stage]
            event: [push]
        parameters:
          repo: dairy-enteligen-admin-ui-app
          skip_existing: false
          tags:
            - 'latest-stage'
            - '${VELA_BUILD_COMMIT:0:7}-stage'

      # Build the Docker image for prod
      - name: build-push-docker-prod
        image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
        ruleset:
          if:
            event: [push]
            branch: [main]
        parameters:
          repo: dairy-enteligen-admin-ui-app
          skip_existing: false
          tags:
            - 'latest-prod'
            - '${VELA_BUILD_COMMIT:0:7}-prod'

      - name: captain-deploy-dev
        image: docker-utilities.binrepo.cglcloud.in/captain:1-stable
        ruleset:
          if:
            event: [ push ]
            branch: [ dev ]
        parameters:
          env: dev
          run_apply: true
          version: '${VELA_BUILD_COMMIT:0:7}-dev'

      - name: captain-deploy-stage
        image: docker-utilities.binrepo.cglcloud.in/captain:1-stable
        ruleset:
          if:
            event: [ push ]
            branch: [ stage ]
        parameters:
          env: stage
          run_apply: true
          version: '${VELA_BUILD_COMMIT:0:7}-stage'

      - name: captain-deploy-prod
        image: docker-utilities.binrepo.cglcloud.in/captain:1-stable
        ruleset:
          if:
            event: [ push ]
            branch: [ main ]
        parameters:
          env: prod
          run_apply: true
          version: '${VELA_BUILD_COMMIT:0:7}-prod'
#   [END build]

#secrets:
#  - name: veracode_user
#    key: EMEA-Tech-Services/dairy-enteligen-admin-ui-app/veracode_user
#    engine: native
#    type: repo
#
#  - name: veracode_pass
#    key: EMEA-Tech-Services/dairy-enteligen-admin-ui-app/veracode_pass
#    engine: native
#    type: repo
