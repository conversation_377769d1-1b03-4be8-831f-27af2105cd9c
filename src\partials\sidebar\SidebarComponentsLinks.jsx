import React from 'react';
import {NavLink} from 'react-router-dom';
import SidebarLinkGroup from '../SidebarLinkGroup.jsx';

function SidebarComponentsLinks({pathname, sidebarExpanded, setSidebarExpanded}) {

  const linkActiveClass = ({isActive}) =>
    'block transition duration-150 truncate ' + (isActive ? 'text-indigo-500' : 'text-slate-400 hover:text-slate-200');

  return (
    <SidebarLinkGroup activecondition={pathname.includes('component')}>
      {(handleClick, open) => {
        return (
          <React.Fragment>
            <a
              href="#"
              className={`block text-slate-200 truncate transition duration-150 ${
                pathname.includes('component') ? 'hover:text-slate-200' : 'hover:text-white'
              }`}
              onClick={(e) => {
                e.preventDefault();
                sidebarExpanded ? handleClick() : setSidebarExpanded(true);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="shrink-0 h-6 w-6" viewBox="0 0 24 24">
                    <circle
                      className={`fill-current ${pathname.includes('component') ? 'text-indigo-500' : 'text-slate-600'}`}
                      cx="16"
                      cy="8"
                      r="8"
                    />
                    <circle
                      className={`fill-current ${pathname.includes('component') ? 'text-indigo-300' : 'text-slate-400'}`}
                      cx="8"
                      cy="16"
                      r="8"
                    />
                  </svg>
                  <span className="text-sm font-medium ml-3 lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                              Components
                            </span>
                </div>
                {/* Icon */}
                <div className="flex shrink-0 ml-2">
                  <svg className={`w-3 h-3 shrink-0 ml-1 fill-current text-slate-400 ${open && 'rotate-180'}`} viewBox="0 0 12 12">
                    <path d="M5.9 11.4L.5 6l1.4-1.4 4 4 4-4L11.3 6z" />
                  </svg>
                </div>
              </div>
            </a>
            <div className="lg:hidden lg:sidebar-expanded:block 2xl:block">
              <ul className={`pl-9 mt-1 ${!open && 'hidden'}`}>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/button"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Button
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/form"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Input Form
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/dropdown"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Dropdown
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/alert"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Alert & Banner
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/modal"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Modal
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/pagination"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Pagination
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/tabs"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Tabs
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/breadcrumb"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Breadcrumb
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/badge"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Badge
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/avatar"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Avatar
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/tooltip"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Tooltip
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/accordion"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Accordion
                              </span>
                  </NavLink>
                </li>
                <li className="mb-1 last:mb-0">
                  <NavLink
                    end
                    to="/component/icons"
                    className={linkActiveClass}
                  >
                              <span className="text-sm font-medium lg:opacity-0 lg:sidebar-expanded:opacity-100 2xl:opacity-100 duration-200">
                                Icons
                              </span>
                  </NavLink>
                </li>
              </ul>
            </div>
          </React.Fragment>
        );
      }}
    </SidebarLinkGroup>
  );
}

export default SidebarComponentsLinks;