import React, {useEffect, useState} from "react";

export const UserAddedCard = (props) => {
  const newUser = props.user;
  const [colorClass, setColorClass] = useState('green-500');
  const [message, setMessage] = useState('');


  useEffect(() => {
    if(newUser.deAppData.active && newUser.dfAppData.active) {
      setColorClass('green-500')
      setMessage('User added');
    } else if(!newUser.deAppData.active && !newUser.dfAppData.active) {
      setColorClass('red-600')
      setMessage('User not added');
    } else {
      setColorClass('yellow-400')
      setMessage('User partially added');
    }
  }, [newUser]);

  return (
      <>
        <div className={'text-center text-' + colorClass + ' font-bold mb-1'}>{message}</div>
        <div className={'outline outline-' + colorClass + ' outline-offset-2 rounded m-2'}>
          <p className="font-bold shadow-md p-1">{newUser.name}</p>
          <p className="shadow-md p-1">{newUser.email}</p>
          <p className="shadow-md p-1">{newUser.country}</p>
          <p className="shadow-md p-1">Discover: <span className="font-bold">{newUser.deAppData.active ? 'yes' : 'no'}</span></p>
          <p className="shadow-md p-1">Forecast: <span className="font-bold">{newUser.dfAppData.active ? 'yes' : 'no'}</span></p>
        </div>
      </>
  )
}