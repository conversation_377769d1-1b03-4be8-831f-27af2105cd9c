import React, {useState} from "react";

const TableHead = ({columns, handleSorting}) => {
  const [sortField, setSortField] = useState("");
  const [order, setOrder] = useState("asc");

  const handleSortingChange = (accessor) => {
    const sortOrder =
        accessor === sortField && order === "asc" ? "desc" : "asc";
    setSortField(accessor);
    setOrder(sortOrder);
    handleSorting(accessor, sortOrder);
  };

  return (
      <thead className="text-xs font-semibold uppercase text-slate-400 dark:text-slate-500 bg-slate-50 dark:bg-slate-700 dark:bg-opacity-50">
      <tr>
        {columns.map(({label, accessor, sortable}) => {
          let cl = "p-2 whitespace-nowrap border-x-2"
          if (sortable){
            if (sortField === accessor && order === "asc") {
              cl += " up";
            } else if (sortField === accessor && order === "desc") {
              cl += " down";
            } else {
              cl += " default";
            }
          }
          
          return (
              <th
                  key={accessor}
                  onClick={sortable ? () => handleSortingChange(accessor) : null}
                  className={cl}
              >
                <div className="font-semibold text-left">{label}</div>
              </th>
        );
        })}
      </tr>
      </thead>
  );
};

export default TableHead;