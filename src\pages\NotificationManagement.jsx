import React, { useState, useEffect } from 'react';
import { useMsal } from '@azure/msal-react';
import NotificationList from '../components/NotificationList.jsx';
import NotificationSetup from '../components/NotificationSetup.jsx';

function NotificationManagement() {
  const { instance } = useMsal();
  const [activeView, setActiveView] = useState('list'); // 'list' or 'setup'
  const [editingNotification, setEditingNotification] = useState(null);

  // TODO: /api/notifications - Fetch notifications from API
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title: 'Release Notes',
      type: 'Release Notes',
      status: 'Draft',
      lastUpdatedBy: 'Chantal M.',
      lastUpdated: '4/25/25 10:33',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam tincidunt, nisl at vestibulum euismod, nunc erat...',
      userSegmentation: 'Internal',
      country: 'All',
      duration: { startDate: '', endDate: '' },
      attachments: []
    },
    {
      id: 2,
      title: 'Special Actions',
      type: 'Special Action',
      status: 'Published',
      lastUpdatedBy: 'Chantal M.',
      lastUpdated: '4/25/25 10:33',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam tincidunt, nisl at vestibulum euismod, nunc erat...',
      userSegmentation: 'Internal',
      country: 'All',
      duration: { startDate: '', endDate: '' },
      attachments: []
    },
    {
      id: 3,
      title: 'Marketing Campaigns',
      type: 'Marketing Campaign',
      status: 'Draft',
      lastUpdatedBy: 'Chantal M.',
      lastUpdated: '4/25/25 10:33',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam tincidunt, nisl at vestibulum euismod, nunc erat...',
      userSegmentation: 'Internal',
      country: 'All',
      duration: { startDate: '', endDate: '' },
      attachments: []
    }
  ]);

  const handleCreateNotification = () => {
    setEditingNotification(null);
    setActiveView('setup');
  };

  const handleEditNotification = (notification) => {
    setEditingNotification(notification);
    setActiveView('setup');
  };

  const handleBackToList = () => {
    setActiveView('list');
    setEditingNotification(null);
  };

  const handleSaveNotification = (notificationData) => {
    // TODO: /api/notifications - Save notification via API
    if (editingNotification) {
      // Update existing notification
      setNotifications(prev => prev.map(n => 
        n.id === editingNotification.id ? { ...notificationData, id: editingNotification.id } : n
      ));
    } else {
      // Create new notification
      const newNotification = {
        ...notificationData,
        id: Date.now(), // Temporary ID generation
        lastUpdatedBy: 'Current User', // TODO: Get from auth context
        lastUpdated: new Date().toLocaleDateString()
      };
      setNotifications(prev => [...prev, newNotification]);
    }
    setActiveView('list');
    setEditingNotification(null);
  };

  const handleDeleteNotification = (id) => {
    // TODO: /api/notifications/{id} - Delete notification via API
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const handleStatusChange = (id, newStatus) => {
    // TODO: /api/notifications/{id}/status - Update notification status via API
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, status: newStatus } : n
    ));
  };

  return (
    <>
      {activeView === 'list' ? (
        <NotificationList
          notifications={notifications}
          onCreateNotification={handleCreateNotification}
          onEditNotification={handleEditNotification}
          onDeleteNotification={handleDeleteNotification}
          onStatusChange={handleStatusChange}
        />
      ) : (
        <NotificationSetup
          notification={editingNotification}
          onSave={handleSaveNotification}
          onCancel={handleBackToList}
        />
      )}
    </>
  );
}

export default NotificationManagement;
